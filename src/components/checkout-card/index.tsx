import React from "react";
import type { CheckoutCardProps } from "@/components/checkout-card/type.ts";
import { Bed, Users, Clock } from "lucide-react";

const CheckoutCard: React.FC<CheckoutCardProps> = ({
  packageData,
  checkIn,
  checkOut,
}) => {
  const taxesAndFees = packageData.taxes.reduce(
    (acc, tax) => acc + parseInt(tax.value),
    0,
  );
  const total = packageData.price + taxesAndFees;

  return (
    <div className="w-full bg-white rounded-lg shadow">
      <img
        className="w-full h-48 object-cover rounded-t-lg"
        src={packageData.unitTypeId.attachments[0]}
        alt="Room"
      />
      <div className="p-6">
        <h2 className="text-gray-700 text-lg font-semibold">
          {packageData.name}
        </h2>
        <p className="text-gray-600 text-sm font-semibold">
          Type: {packageData.unitTypeId.name}
        </p>

        <div className="flex items-center text-gray-600 text-sm mt-2 space-x-4">
          <div className="flex items-center space-x-1">
            <Users size={16} />
            <span>{packageData.noOfAdults}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Bed size={16} />
            <span>{packageData.unitTypeId.bedType}</span>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4">
          <span className="text-gray-700">${packageData.price}</span>
          <span className="text-gray-500">
            (${taxesAndFees} taxes and fees)
          </span>
        </div>

        <p className="text-gray-900 font-bold mt-2">Total: ${total}/hr</p>

        <div className="flex items-center text-gray-600 text-sm mt-2 space-x-4">
          <div className="flex items-center space-x-1">
            <Clock size={16} />
            <span>Check-in: {new Date(checkIn).toLocaleTimeString()}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock size={16} />
            <span>Check-out: {new Date(checkOut).toLocaleTimeString()}</span>
          </div>
        </div>

        <button
          type="submit"
          className="w-full bg-blue-500 text-white py-2 mt-4 rounded hover:bg-blue-600"
        >
          Proceed To Pay
        </button>
      </div>
    </div>
  );
};

export default CheckoutCard;
