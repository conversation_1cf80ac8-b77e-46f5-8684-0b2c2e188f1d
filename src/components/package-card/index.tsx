import {
  Info,
  LucideBedDouble,
  LucideCheck,
  LucideSquareDot,
  Minus,
  Plus,
  ShoppingCart,
} from "lucide-react";
import React, { useState } from "react";
import type { PackageCardProps } from "@/components/package-card/type.ts";

const PackageCard: React.FC<PackageCardProps> = ({
  packageData,
  onPackageChange,
  selectedPackages,
}) => {
  const unitType = packageData.unitTypeId;
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationType, setAnimationType] = useState<"add" | "remove" | null>(
    null,
  );
  const [showSuccess, setShowSuccess] = useState(false);

  const taxesAndFees = packageData.taxes.reduce(
    (acc, tax) => acc + parseInt(tax.value),
    0,
  );
  const totalPrice = packageData.price + taxesAndFees;

  const isSelected = selectedPackages?.some(
    (pkg) => pkg._id === packageData._id,
  );
  const selectedCount =
    selectedPackages?.filter((pkg) => pkg._id === packageData._id).length || 0;

  const handlePackageAction = (action: "add" | "remove") => {
    setIsAnimating(true);
    setAnimationType(action);

    if (action === "add") {
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 600);
    }

    setTimeout(() => {
      onPackageChange(packageData, action);
      setIsAnimating(false);
      setAnimationType(null);
    }, 200);
  };

  return (
    <div
      className={`h-[650px] bg-white rounded-xl shadow-md transition-all duration-300 hover:shadow-xl hover:scale-[1.02] ${
        isSelected ? "ring-2 ring-primary ring-opacity-50 shadow-lg" : ""
      } ${isAnimating ? (animationType === "add" ? "animate-scale-in" : "animate-shake") : ""}`}
    >
      <div className="relative">
        <img
          className="w-full h-60 object-cover rounded-t-xl"
          src={unitType.attachments[0]}
          alt={unitType.name}
        />
        {isSelected && (
          <div className="absolute top-3 right-3 bg-green-500 text-white rounded-full p-2 animate-bounce-in">
            <ShoppingCart className="w-4 h-4" />
          </div>
        )}
        {showSuccess && (
          <div className="absolute inset-0 bg-green-500/20 rounded-t-xl flex items-center justify-center animate-pulse-success">
            <div className="bg-white rounded-full p-3 shadow-lg">
              <LucideCheck className="w-8 h-8 text-green-500" />
            </div>
          </div>
        )}
      </div>
      <div className="p-6">
        <div className="flex flex-col gap-2 justify-between">
          <h2 className="text-xl font-bold">{packageData.name}</h2>
          <div className="flex items-center space-x-2 text-gray-500 text-sm">
            <LucideSquareDot className="w-5 h-5" />
            <span>{unitType.area}</span>
            <LucideBedDouble className="w-5 h-5" />
            <span>
              {unitType.bedType} (Sleeps {packageData.noOfAdults} Adults)
            </span>
          </div>
        </div>
        <div className="mt-4">
          <h3 className="text-lg font-semibold">Included:</h3>
          <ul className="mt-2 space-y-2 text-gray-500 text-sm">
            {packageData.amenities &&
              packageData.amenities.slice(0, 3).map((amenity, index) => (
                <li key={index} className="flex items-center space-x-2">
                  <LucideCheck className="w-5 h-5 text-green-500" />
                  <span>{amenity.name}</span>
                </li>
              ))}
          </ul>
          <a className="text-gray-500 hover:text-primary text-sm mt-2 inline-block">
            View more
          </a>
        </div>
        <div className="mt-4">
          <div className="flex items-center gap-4">
            <p className="text-lg font-medium">${packageData.price}</p>
            <p className="text-gray-500 text-sm flex items-center gap-1">
              <Info className="w-4 h-4" />{" "}
              <span>(${taxesAndFees} taxes and fees)</span>
            </p>
          </div>
          <p className="text-xl font-bold">Total: ${totalPrice}/hr</p>
        </div>
        <div className="mt-4 w-full">
          {isSelected ? (
            <div className="flex items-center justify-between py-1 px-4 rounded-lg border-2 border-primary bg-primary/5 animate-slide-in-up">
              <button
                onClick={() => handlePackageAction("remove")}
                disabled={isAnimating}
                className="p-2 hover:bg-red-100 text-red-600 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-110 active:scale-95"
                aria-label="Remove package"
              >
                <Minus size={20} />
              </button>
              <div className="flex flex-col items-center">
                <span
                  className={`text-lg font-bold text-primary transition-all duration-300 ${
                    isAnimating && animationType === "add"
                      ? "animate-bounce"
                      : ""
                  }`}
                >
                  {selectedCount}
                </span>
                <span className="text-xs text-gray-500">Selected</span>
              </div>
              <button
                onClick={() => handlePackageAction("add")}
                disabled={isAnimating}
                className="p-2 hover:bg-green-100 text-green-600 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-110 active:scale-95"
                aria-label="Add another package"
              >
                <Plus size={20} />
              </button>
            </div>
          ) : (
            <button
              onClick={() => handlePackageAction("add")}
              disabled={isAnimating}
              className="w-full bg-gradient-to-r from-primary to-primary-dark text-white py-3 rounded-lg hover:shadow-lg transform hover:scale-[1.02] transition-all duration-300 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed active:scale-[0.98] group"
            >
              <span className="flex items-center justify-center gap-2">
                <ShoppingCart className="w-5 h-5 group-hover:animate-bounce" />
                Select Package
              </span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PackageCard;
