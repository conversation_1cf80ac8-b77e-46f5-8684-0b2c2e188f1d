import { Plane, Trash2, User } from "lucide-react";
import UiInput from "@/elements/UiInput";
import React from "react";
import { useFormContext } from "react-hook-form";
import type { GuestFormValues } from "./type";

const GuestForm: React.FC = () => {
  const { getValues, setValue, watch } = useFormContext<GuestFormValues>();

  const addGuest = () => {
    const currentGuests = getValues("guests") || [];
    setValue(
      "guests",
      [
        ...currentGuests,
        {
          firstName: "",
          lastName: "",
          email: "",
          phone: "",
        },
      ],
      { shouldValidate: true },
    );
  };

  const removeGuest = (index: number) => {
    const currentGuests = getValues("guests") || [];
    if (currentGuests.length > 1) {
      const updatedGuests = currentGuests.filter((_, i) => i !== index);
      setValue("guests", updatedGuests, { shouldValidate: true });
    }
  };

  const guests = watch("guests") || [];

  return (
    <div className="bg-white rounded-2xl shadow-md border border-gray-100 overflow-hidden sticky top-10">
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-white rounded-lg shadow-sm">
              <User className="w-5 h-5 text-blue-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900">
              Guest Details
            </h2>
          </div>
          <button
            type="button"
            onClick={addGuest}
            className="p-2 border border-primary text-primary rounded-lg"
          >
            Add Guest
          </button>
        </div>
        {guests.map((_, index) => (
          <div key={`guest-${index}`} className="mb-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">
                Guest {index + 1}
              </h3>
              {guests.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeGuest(index)}
                  className="p-2 text-red-600 hover:text-red-800"
                  title="Remove Guest"
                >
                  <Trash2 className="w-5 h-5" />
                </button>
              )}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <UiInput
                name={`guests[${index}].firstName`}
                label="First Name"
                placeholder="Enter First Name"
                rules={{ required: "First Name is required" }}
              />
              <UiInput
                name={`guests[${index}].lastName`}
                label="Last Name"
                placeholder="Enter Last Name"
                rules={{ required: "Last Name is required" }}
              />
              <UiInput
                name={`guests[${index}].email`}
                label="Email"
                type="email"
                placeholder="Enter Email"
                rules={{
                  required: "Email is required",
                  pattern: {
                    value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                    message: "Invalid email address",
                  },
                }}
              />
              <UiInput
                name={`guests[${index}].phone`}
                label="Phone"
                type="tel"
                placeholder="Enter Phone Number"
                rules={{ required: "Phone Number is required" }}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Flight Details Section */}
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-blue-50 rounded-lg">
            <Plane className="w-5 h-5 text-blue-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900">
            Flight Details
          </h2>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <UiInput
              name="flightNumber"
              label="Flight Number"
              placeholder="Enter Flight Number"
              rules={{ required: "Flight Number is required" }}
            />
            <UiInput
              name="fromLocation"
              label="From"
              placeholder="Search for a location"
              rules={{ required: "From location is required" }}
            />
            <UiInput
              name="toLocation"
              label="To"
              placeholder="Search for a location"
              rules={{ required: "To location is required" }}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UiInput
              name="departureDateTime"
              label="Departure Date and Time"
              type="datetime-local"
              rules={{ required: "Departure Date and Time is required" }}
            />
            <UiInput
              name="arrivalDateTime"
              label="Arrival Date and Time"
              type="datetime-local"
              rules={{ required: "Arrival Date and Time is required" }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GuestForm;
