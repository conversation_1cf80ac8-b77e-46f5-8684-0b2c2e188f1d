import GuestForm from "@/pages/checkout/guest-form";
import Header from "@/components/header";
import CheckoutCard from "@/components/checkout-card";
import type { GuestFormValues } from "@/pages/checkout/guest-form/type";
import { FormProvider, useForm } from "react-hook-form";
import { useLocation } from "react-router";
import type { CheckoutLocationState } from "@/pages/checkout/types.ts";

const CheckoutPage = () => {
  const location = useLocation();
  const { state } = location;
  const { selectedPackages, checkInDate, checkOutDate }: CheckoutLocationState =
    state;

  const methods = useForm<GuestFormValues>({
    defaultValues: {
      guests: [
        {
          firstName: "",
          lastName: "",
          email: "",
          phone: "",
        },
      ],
      flightNumber: "",
      fromLocation: "",
      toLocation: "",
      departureDateTime: "",
      arrivalDateTime: "",
    },
  });

  const onSubmit = (_data: GuestFormValues) => {};

  return (
    <div>
      <Header />
      <FormProvider {...methods}>
        <form
          onSubmit={methods.handleSubmit(onSubmit)}
          className="w-full bg-white"
        >
          <div className="px-6 py-4 flex flex-col md:flex-row gap-4 justify-between">
            <div className="w-full flex-3">
              <GuestForm />
            </div>
            <div className="w-full flex-1">
              <h1 className={"text-lg mb-2 font-medium"}>
                Your Services Summary
              </h1>
              <div className="space-y-4">
                {selectedPackages.map((pkg) => (
                  <CheckoutCard
                    key={pkg._id}
                    packageData={pkg}
                    checkIn={checkInDate}
                    checkOut={checkOutDate}
                  />
                ))}
              </div>
            </div>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default CheckoutPage;
